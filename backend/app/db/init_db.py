from sqlalchemy import create_engine
from sqlalchemy.orm import Session
import os
from dotenv import load_dotenv

from app.db.session import Base, engine
from app.models import platform, architecture, release, update_check, download_record, user, setting, project

# 加载环境变量
load_dotenv()

# 创建所有表
def create_tables():
    Base.metadata.create_all(bind=engine)

# 初始化平台数据
def init_platforms(db: Session):
    from app.models.platform import Platform
    
    # 检查是否已存在数据
    if db.query(Platform).count() > 0:
        return
    
    # 创建初始平台数据
    platforms = [
        Platform(
            name="Windows",
            identifier="windows",
            description="Microsoft Windows操作系统",
            is_active=True
        ),
        Platform(
            name="macOS",
            identifier="macos",
            description="Apple macOS操作系统",
            is_active=True
        ),
        Platform(
            name="Linux",
            identifier="linux",
            description="Linux操作系统",
            is_active=True
        ),
    ]
    
    db.add_all(platforms)
    db.commit()

# 初始化架构数据
def init_architectures(db: Session):
    from app.models.architecture import Architecture
    
    # 检查是否已存在数据
    if db.query(Architecture).count() > 0:
        return
    
    # 创建初始架构数据
    architectures = [
        Architecture(
            name="x64",
            identifier="x64",
            description="64位x86架构",
            is_active=True
        ),
        Architecture(
            name="ARM64",
            identifier="aarch64",
            description="64位ARM架构",
            is_active=True
        ),
    ]
    
    db.add_all(architectures)
    db.commit()

# 初始化管理员用户
def init_admin(db: Session):
    from app.models.user import User
    
    # 检查是否已存在管理员用户
    if db.query(User).filter(User.is_admin == True).count() > 0:
        return
    
    # 从环境变量获取管理员信息
    admin_username = os.getenv("ADMIN_USERNAME", "spencer")
    admin_password = os.getenv("ADMIN_PASSWORD", "Pp250509")
    admin_email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
    
    # 创建管理员用户
    admin_user = User(
        username=admin_username,
        email=admin_email,
        hashed_password=User.get_password_hash(admin_password),
        is_active=True,
        is_admin=True
    )
    
    db.add(admin_user)
    db.commit()

# 初始化设置
def init_settings(db: Session):
    from app.api.endpoints.settings import DEFAULT_SETTINGS
    from app.models.setting import Setting
    
    # 检查是否已存在设置
    if db.query(Setting).count() > 0:
        return
    
    # 创建初始设置
    for key, default in DEFAULT_SETTINGS.items():
        Setting.set_value(db, key, default["value"], default["description"])

# 初始化数据库
def init_db(db: Session):
    # 创建表
    create_tables()
    
    # 初始化数据
    init_platforms(db)
    init_architectures(db)
    init_admin(db)
    init_settings(db)
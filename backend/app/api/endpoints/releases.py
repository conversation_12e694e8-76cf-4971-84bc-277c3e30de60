from typing import List
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
from datetime import datetime

def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    try:
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        return 0
    except Exception:
        return 0

from app.db.session import get_db
from app.models.release import Release
from app.models.platform import Platform
from app.models.architecture import Architecture
from app.schemas.release import ReleaseSchema, ReleaseCreate, ReleaseUpdate
from app.core.auth import get_current_admin_user
from app.core.config import settings
from app.models.user import User

router = APIRouter()

@router.get("/")
def get_releases(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    platform_id: Optional[int] = None,
    architecture_id: Optional[int] = None,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取所有发布版本列表"""
    query = db.query(Release)
    
    # 搜索过滤
    if search:
        query = query.filter(
            Release.version.contains(search) |
            Release.title.contains(search)
        )
    
    # 平台过滤
    if platform_id:
        query = query.filter(Release.platforms.any(Platform.id == platform_id))
    
    # 架构过滤
    if architecture_id:
        query = query.filter(Release.architectures.any(Architecture.id == architecture_id))
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    releases = query.offset(skip).limit(limit).all()
    
    # 转换为字典格式，确保关联数据被正确序列化
    items = []
    for release in releases:
        release_dict = {
            "id": release.id,
            "version": release.version,
            "title": release.title,
            "notes": release.notes,
            "update_file": release.update_file,
            "signature": release.signature,
            "is_active": release.is_active,
            "is_prerelease": release.is_prerelease,
            "pub_date": release.pub_date,
            "created_at": release.created_at,
            "updated_at": release.updated_at,
            "platform": {
                "id": release.platforms[0].id if release.platforms else None,
                "name": release.platforms[0].name if release.platforms else "未知平台",
                "identifier": release.platforms[0].identifier if release.platforms else ""
            } if release.platforms else {"id": None, "name": "未知平台", "identifier": ""},
            "architecture": {
                "id": release.architectures[0].id if release.architectures else None,
                "name": release.architectures[0].name if release.architectures else "未知架构",
                "identifier": release.architectures[0].identifier if release.architectures else ""
            } if release.architectures else {"id": None, "name": "未知架构", "identifier": ""},
            "platforms": [{
                "id": p.id,
                "name": p.name,
                "identifier": p.identifier,
                "description": p.description,
                "is_active": p.is_active,
                "created_at": p.created_at,
                "updated_at": p.updated_at
            } for p in release.platforms],
            "architectures": [{
                "id": a.id,
                "name": a.name,
                "identifier": a.identifier,
                "description": a.description,
                "is_active": a.is_active,
                "created_at": a.created_at,
                "updated_at": a.updated_at
            } for a in release.architectures],
            "download_url": f"/p-box/api/download/{release.version}/{release.platforms[0].identifier if release.platforms else 'unknown'}/{release.architectures[0].identifier if release.architectures else 'unknown'}/" if release.update_file else None,
            "fileSize": get_file_size(release.update_file) if release.update_file else 0,
            "downloadCount": len(release.download_records) if hasattr(release, 'download_records') else 0,
            "releaseDate": release.pub_date
        }
        items.append(release_dict)
    
    return {
        "items": items,
        "total": total,
        "totalPages": (total + limit - 1) // limit,
        "currentPage": (skip // limit) + 1
    }

@router.get("/{release_id}")
def get_release(release_id: int, current_user: User = Depends(get_current_admin_user), db: Session = Depends(get_db)):
    """获取特定发布版本详情"""
    release = db.query(Release).filter(Release.id == release_id).first()
    if release is None:
        raise HTTPException(status_code=404, detail="Release not found")
    
    # 返回与get_releases相同的数据结构
    return {
        "id": release.id,
        "version": release.version,
        "title": release.title,
        "notes": release.notes,
        "pub_date": release.pub_date,
        "is_active": release.is_active,
        "is_prerelease": release.is_prerelease,
        "update_file": release.update_file,
        "download_url": f"/p-box/api/download/{release.version}/{release.platforms[0].identifier if release.platforms else 'unknown'}/{release.architectures[0].identifier if release.architectures else 'unknown'}/" if release.update_file else None,
        "created_at": release.created_at,
        "updated_at": release.updated_at,
        "platform": {
            "id": release.platforms[0].id if release.platforms else None,
            "name": release.platforms[0].name if release.platforms else "未知平台",
            "identifier": release.platforms[0].identifier if release.platforms else ""
        } if release.platforms else {"id": None, "name": "未知平台", "identifier": ""},
        "architecture": {
            "id": release.architectures[0].id if release.architectures else None,
            "name": release.architectures[0].name if release.architectures else "未知架构",
            "identifier": release.architectures[0].identifier if release.architectures else ""
        } if release.architectures else {"id": None, "name": "未知架构", "identifier": ""},
        "platforms": [{
            "id": p.id,
            "name": p.name,
            "identifier": p.identifier,
            "description": p.description,
            "is_active": p.is_active,
            "created_at": p.created_at,
            "updated_at": p.updated_at
        } for p in release.platforms],
        "architectures": [{
            "id": a.id,
            "name": a.name,
            "identifier": a.identifier,
            "description": a.description,
            "is_active": a.is_active,
            "created_at": a.created_at,
            "updated_at": a.updated_at
        } for a in release.architectures],
        "fileSize": get_file_size(release.update_file) if release.update_file else 0,
        "downloadCount": len(release.download_records) if hasattr(release, 'download_records') else 0,
        "releaseDate": release.pub_date
    }

@router.post("/", response_model=ReleaseSchema)
async def create_release(
    version: str = Form(...),
    title: str = Form(...),
    notes: Optional[str] = Form(None),
    signature: Optional[str] = Form(None),
    platform_ids: str = Form(...),  # 逗号分隔的平台ID
    architecture_ids: str = Form(...),  # 逗号分隔的架构ID
    is_active: bool = Form(True),
    is_prerelease: bool = Form(False),
    pub_date: Optional[str] = Form(None),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """创建新的发布版本"""
    # 检查版本是否已存在
    existing_release = db.query(Release).filter(Release.version == version).first()
    if existing_release:
        raise HTTPException(status_code=400, detail="Version already exists")
    
    # 解析平台和架构ID
    try:
        platform_id_list = [int(id.strip()) for id in platform_ids.split(',') if id.strip()]
        architecture_id_list = [int(id.strip()) for id in architecture_ids.split(',') if id.strip()]
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid platform or architecture IDs")
    
    # 验证平台和架构是否存在
    platforms = db.query(Platform).filter(Platform.id.in_(platform_id_list)).all()
    architectures = db.query(Architecture).filter(Architecture.id.in_(architecture_id_list)).all()
    
    if len(platforms) != len(platform_id_list):
        raise HTTPException(status_code=400, detail="Some platforms not found")
    if len(architectures) != len(architecture_id_list):
        raise HTTPException(status_code=400, detail="Some architectures not found")
    
    # 保存上传的文件
    upload_dir = settings.UPLOAD_DIR
    os.makedirs(upload_dir, exist_ok=True)
    
    file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
    file_name = f"{version}_{file.filename}" if file.filename else f"{version}{file_extension}"
    file_path = os.path.join(upload_dir, file_name)
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # 解析发布日期
    parsed_pub_date = datetime.now()
    if pub_date:
        try:
            parsed_pub_date = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))
        except ValueError:
            parsed_pub_date = datetime.now()
    
    # 创建发布版本
    release = Release(
        version=version,
        title=title,
        notes=notes,
        update_file=file_path,
        signature=signature,
        is_active=is_active,
        is_prerelease=is_prerelease,
        pub_date=parsed_pub_date
    )
    
    # 添加平台和架构关联
    release.platforms = platforms
    release.architectures = architectures
    
    db.add(release)
    db.commit()
    db.refresh(release)
    
    return release

@router.put("/{release_id}", response_model=ReleaseSchema)
async def update_release(
    release_id: int,
    version: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    notes: Optional[str] = Form(None),
    signature: Optional[str] = Form(None),
    platform_ids: Optional[str] = Form(None),
    architecture_ids: Optional[str] = Form(None),
    is_active: Optional[bool] = Form(None),
    is_prerelease: Optional[bool] = Form(None),
    pub_date: Optional[str] = Form(None),
    file: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """更新发布版本"""
    release = db.query(Release).filter(Release.id == release_id).first()
    if not release:
        raise HTTPException(status_code=404, detail="Release not found")
    
    # 更新基本信息
    if version is not None:
        # 检查新版本号是否已存在（排除当前版本）
        existing = db.query(Release).filter(
            Release.version == version,
            Release.id != release_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="Version already exists")
        release.version = version
    
    if title is not None:
        release.title = title
    if notes is not None:
        release.notes = notes
    if signature is not None:
        release.signature = signature
    if is_active is not None:
        release.is_active = is_active
    if is_prerelease is not None:
        release.is_prerelease = is_prerelease
    
    if pub_date is not None:
        try:
            release.pub_date = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))
        except ValueError:
            pass  # 保持原有日期
    
    # 更新平台和架构关联
    if platform_ids is not None:
        try:
            platform_id_list = [int(id.strip()) for id in platform_ids.split(',') if id.strip()]
            platforms = db.query(Platform).filter(Platform.id.in_(platform_id_list)).all()
            if len(platforms) != len(platform_id_list):
                raise HTTPException(status_code=400, detail="Some platforms not found")
            release.platforms = platforms
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid platform IDs")
    
    if architecture_ids is not None:
        try:
            architecture_id_list = [int(id.strip()) for id in architecture_ids.split(',') if id.strip()]
            architectures = db.query(Architecture).filter(Architecture.id.in_(architecture_id_list)).all()
            if len(architectures) != len(architecture_id_list):
                raise HTTPException(status_code=400, detail="Some architectures not found")
            release.architectures = architectures
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid architecture IDs")
    
    # 更新文件
    if file is not None:
        # 删除旧文件
        if release.update_file and os.path.exists(release.update_file):
            try:
                os.remove(release.update_file)
            except OSError:
                pass  # 忽略删除失败
        
        # 保存新文件
        upload_dir = settings.UPLOAD_DIR
        os.makedirs(upload_dir, exist_ok=True)
        
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        file_name = f"{release.version}_{file.filename}" if file.filename else f"{release.version}{file_extension}"
        file_path = os.path.join(upload_dir, file_name)
        
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            release.update_file = file_path
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    db.commit()
    db.refresh(release)
    
    return release

@router.delete("/{release_id}")
def delete_release(
    release_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """删除发布版本"""
    release = db.query(Release).filter(Release.id == release_id).first()
    if not release:
        raise HTTPException(status_code=404, detail="Release not found")
    
    # 删除关联的文件
    if release.update_file and os.path.exists(release.update_file):
        try:
            os.remove(release.update_file)
        except OSError:
            pass  # 忽略删除失败
    
    db.delete(release)
    db.commit()
    
    return {"message": "Release deleted successfully"}
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
from pathlib import Path

from app.core.auth import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectSchema, ProjectWithCreator

router = APIRouter()

# 配置文件上传目录
UPLOAD_DIR = Path("uploads/configs")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

@router.get("/", response_model=List[ProjectSchema])
async def get_user_projects(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取当前用户的项目列表"""
    projects = db.query(Project).filter(
        Project.creator_user_id == current_user.id
    ).offset(skip).limit(limit).all()
    return projects

@router.post("/", response_model=ProjectSchema, status_code=status.HTTP_201_CREATED)
async def create_project(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    config_file: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建新项目"""
    # 检查项目名称是否已存在（同一用户下）
    existing_project = db.query(Project).filter(
        Project.name == name,
        Project.creator_user_id == current_user.id
    ).first()

    if existing_project:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project name already exists"
        )

    # 处理配置文件上传
    config_file_id = None
    if config_file and config_file.filename:
        # 检查文件类型
        allowed_extensions = {".json", ".yaml", ".yml", ".toml", ".ini", ".conf"}
        file_extension = Path(config_file.filename).suffix.lower()

        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Allowed types: json, yaml, yml, toml, ini, conf"
            )

        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        config_file_id = f"{file_id}{file_extension}"
        file_path = UPLOAD_DIR / config_file_id

        # 保存文件
        try:
            with open(file_path, "wb") as buffer:
                content = await config_file.read()
                buffer.write(content)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save config file"
            )

    # 创建新项目
    db_project = Project(
        name=name,
        description=description,
        creator_user_id=current_user.id,
        config_file_id=config_file_id
    )

    db.add(db_project)
    db.commit()
    db.refresh(db_project)

    return db_project

@router.get("/{project_id}", response_model=ProjectSchema)
async def get_project(
    project_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取特定项目信息"""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.creator_user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return project

@router.put("/{project_id}", response_model=ProjectSchema)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新项目信息"""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.creator_user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # 检查项目名称是否已存在（同一用户下，排除当前项目）
    if project_update.name is not None:
        existing_project = db.query(Project).filter(
            Project.name == project_update.name,
            Project.creator_user_id == current_user.id,
            Project.id != project_id
        ).first()
        
        if existing_project:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project name already exists"
            )
        project.name = project_update.name
    
    if project_update.description is not None:
        project.description = project_update.description
    
    if project_update.config_file_id is not None:
        project.config_file_id = project_update.config_file_id
    
    db.commit()
    db.refresh(project)
    
    return project

@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除项目"""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.creator_user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # 删除关联的配置文件
    if project.config_file_id:
        config_file_path = UPLOAD_DIR / project.config_file_id
        if config_file_path.exists():
            config_file_path.unlink()
    
    db.delete(project)
    db.commit()
    
    return None

@router.post("/upload-config", response_model=dict)
async def upload_config_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """上传配置文件"""
    # 检查文件类型
    allowed_extensions = {".json", ".yaml", ".yml", ".toml", ".ini", ".conf"}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type. Allowed types: json, yaml, yml, toml, ini, conf"
        )
    
    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    file_path = UPLOAD_DIR / f"{file_id}{file_extension}"
    
    # 保存文件
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )
    
    return {
        "file_id": f"{file_id}{file_extension}",
        "filename": file.filename,
        "size": len(content)
    }
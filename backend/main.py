from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
import logging

from app.api.api import api_router
from app.core.config import settings
from app.db.session import SessionLocal
from app.db.init_db import init_db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加API路由
app.include_router(api_router, prefix=settings.API_PREFIX)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 根路由重定向到文档
@app.get("/")
async def root():
    return {"message": "Welcome to P-Box Update Server API"}

def check_and_init_database():
    """
    检查数据库是否存在，如果不存在则进行初始化
    """
    # 从DATABASE_URL中提取数据库文件路径
    db_url = settings.DATABASE_URL
    if db_url.startswith("sqlite:///"):
        db_file_path = db_url.replace("sqlite:///", "")
        
        # 检查数据库文件是否存在
        if not os.path.exists(db_file_path):
            logger.info(f"数据库文件不存在: {db_file_path}")
            logger.info("开始初始化数据库...")
            
            try:
                # 创建数据库会话
                db = SessionLocal()
                
                # 初始化数据库
                init_db(db)
                
                logger.info("数据库初始化完成")
                
            except Exception as e:
                logger.error(f"数据库初始化失败: {e}")
                raise e
            finally:
                db.close()
        else:
            logger.info(f"数据库文件已存在: {db_file_path}")
    else:
        logger.info("非SQLite数据库，跳过文件存在性检查")

if __name__ == "__main__":
    # 启动前检查并初始化数据库
    check_and_init_database()
    
    uvicorn.run("main:app", host=settings.HOST, port=settings.PORT, reload=True)
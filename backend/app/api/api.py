from fastapi import APIRouter
from app.api.endpoints import platforms, architectures, releases, updates, downloads, auth, stats, settings, projects

api_router = APIRouter()

# 添加各个端点的路由
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(platforms.router, prefix="/platforms", tags=["platforms"])
api_router.include_router(architectures.router, prefix="/architectures", tags=["architectures"])
api_router.include_router(releases.router, prefix="/releases", tags=["releases"])
api_router.include_router(releases.router, prefix="/versions", tags=["versions"])  # 版本接口别名
api_router.include_router(updates.router, prefix="/check-update", tags=["updates"])
api_router.include_router(downloads.router, prefix="/download", tags=["downloads"])
api_router.include_router(stats.router, prefix="/stats", tags=["stats"])
api_router.include_router(settings.router, prefix="/settings", tags=["settings"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
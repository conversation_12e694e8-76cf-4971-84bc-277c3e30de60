import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { User, AuthContextType, LoginRequest, RegisterRequest } from '../types';
import { apiClient } from '../api';
import { 
  setAuthToken, 
  getAuthToken, 
  setUserInfo, 
  getUserInfo, 
  clearAuthData, 
  isAuthenticated as checkIsAuthenticated 
} from '../utils/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const savedToken = getAuthToken();
        const savedUser = getUserInfo();
        
        if (savedToken && savedUser && checkIsAuthenticated()) {
          setToken(savedToken);
          setUser(savedUser);
          
          // 验证token是否仍然有效，获取最新用户信息
          try {
            const currentUser = await apiClient.getCurrentUser();
            setUser(currentUser);
            setUserInfo(currentUser);
          } catch (error) {
            console.error('Token validation failed:', error);
            clearAuthData();
            setToken(null);
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setIsLoading(true);
      const authResponse = await apiClient.login(credentials);
      
      // 存储token
      setAuthToken(authResponse.access_token);
      setToken(authResponse.access_token);
      
      // 获取用户信息
      const userInfo = await apiClient.getCurrentUser();
      setUser(userInfo);
      setUserInfo(userInfo);
      
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest): Promise<void> => {
    try {
      setIsLoading(true);

      // 先注册用户（不包含头像）
      const { avatar, ...userDataWithoutAvatar } = userData;
      await apiClient.register(userDataWithoutAvatar);

      // 注册成功后自动登录
      await login({
        username: userData.username,
        password: userData.password,
        remember_me: false
      });

      // 如果有头像，登录成功后上传头像
      if (avatar) {
        try {
          await apiClient.uploadAvatar(avatar);
          // 刷新用户信息以获取最新的头像URL
          await refreshUser();
        } catch (avatarError) {
          console.error('Avatar upload failed:', avatarError);
          // 头像上传失败不影响注册流程，只记录错误
        }
      }

    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    clearAuthData();
    setToken(null);
    setUser(null);
  };

  const refreshUser = async (): Promise<void> => {
    try {
      if (token && checkIsAuthenticated()) {
        const currentUser = await apiClient.getCurrentUser();
        setUser(currentUser);
        setUserInfo(currentUser);
      }
    } catch (error) {
      console.error('Failed to refresh user info:', error);
      // 如果刷新失败，可能token已过期，清除认证信息
      clearAuthData();
      setToken(null);
      setUser(null);
    }
  };

  const isAuthenticated = Boolean(token && user && checkIsAuthenticated());

  const value: AuthContextType = {
    user,
    token,
    login,
    register,
    logout,
    refreshUser,
    isLoading,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

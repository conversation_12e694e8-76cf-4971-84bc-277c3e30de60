from sqlalchemy import Column, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from passlib.context import CryptContext

from app.db.session import Base

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    avatar_url = Column(String, nullable=True)  # 头像URL
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    projects = relationship("Project", back_populates="creator")
    
    def __repr__(self):
        return f"<User {self.username}>"
    
    @staticmethod
    def verify_password(plain_password, hashed_password):
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password):
        """获取密码哈希"""
        return pwd_context.hash(password)
export interface Platform {
  id: number;
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Architecture {
  id: number;
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 发布版本类型
export interface Release {
  id: number;
  version: string;
  title: string;
  notes: string;
  platform?: {
    id: number;
    name: string;
    identifier: string;
  };
  architecture?: {
    id: number;
    name: string;
    identifier: string;
  };
  platforms?: Platform[];
  architectures?: Architecture[];
  update_file: string;
  signature: string;
  is_active: boolean;
  is_prerelease: boolean;
  pub_date: string;
  created_at: string;
  updated_at: string;
  download_url?: string;
  fileSize?: number;
  downloadCount?: number;
  releaseDate?: string;
}

// 更新检查记录类型
export interface UpdateCheck {
  id: number;
  client_ip: string;
  platform: string;
  architecture: string;
  current_version: string;
  user_agent: string;
  has_update: boolean;
  new_version: string;
  uuid: string;
  install_time: string;
  created_at: string;
}

// 下载记录类型
export interface DownloadRecord {
  id: number;
  release: Release;
  client_ip: string;
  platform: string;
  architecture: string;
  created_at: string;
}

// 表单数据类型
export interface PlatformFormData {
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
}

export interface ArchitectureFormData {
  name: string;
  identifier: string;
  description: string;
  is_active: boolean;
}

export interface ReleaseFormData {
  version: string;
  title: string;
  notes: string;
  platform_ids: number[];
  architecture_ids: number[];
  update_file: File | null;
  signature: string;
  is_active: boolean;
  is_prerelease: boolean;
  pub_date: string;
}

// 统计数据类型
export interface BasicStats {
  total_downloads: number;
  monthly_downloads: number;
  version_count: number;
  platform_count: number;
  architecture_count: number;
  total_update_checks: number;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  avatar_url?: string;
  is_active: boolean;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  avatar?: File | null;
  is_active?: boolean;
  is_admin?: boolean;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  totalPages: number;
  currentPage: number;
}

// 更新检查图表数据类型
export interface UpdateCheckChartData {
  date: string;
  total: number;
  [platform: string]: number | string; // 动态平台字段
}

export interface UpdateCheckChartResponse {
  data: UpdateCheckChartData[];
  platforms: string[];
}

// 项目相关类型
export interface Project {
  id: number;
  name: string;
  description?: string;
  creator_user_id: number;
  config_file_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ProjectFormData {
  name: string;
  description?: string;
  config_file?: File | null;
}

export interface ProjectUpdate {
  name?: string;
  description?: string;
  config_file_id?: string;
}

// 密码修改类型
export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}
